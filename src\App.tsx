import { Toaster } from "@/components/shared/ui/toaster";
import { Toaster as Sonner } from "@/components/shared/ui/sonner";
import { TooltipProvider } from "@/components/shared/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import PublicRoute from "@/components/auth/PublicRoute";

// Authentication Pages
import BusinessLogin from "./pages/auth/BusinessLogin";
import AdminLogin from "./pages/auth/AdminLogin";
import BusinessRegister from "./pages/auth/BusinessRegister";
import BusinessForgotPassword from "./pages/auth/BusinessForgotPassword";
import AdminForgotPassword from "./pages/auth/AdminForgotPassword";
import BusinessResetPassword from "./pages/auth/BusinessResetPassword";
import AdminResetPassword from "./pages/auth/AdminResetPassword";

// Dashboard Pages
import BusinessDashboard from "./pages/BusinessDashboard";
import AdminDashboard from "./pages/AdminDashboard";

// Legacy Pages (for existing business features)
import { Layout } from "@/components/shared/layout/Layout";
import Dashboard from "./pages/Dashboard";
import Inbox from "./pages/Inbox";
import Automation from "./pages/Automation";
import Templates from "./pages/Templates";
import Campaigns from "./pages/Campaigns";
import Contacts from "./pages/Contacts";
import Analytics from "./pages/Analytics";
import PhoneNumbers from "./pages/PhoneNumbers";
import Billing from "./pages/Billing";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <Routes>
            {/* Root redirect */}
            <Route
              path="/"
              element={<Navigate to="/business/auth/login" replace />}
            />

            {/* Business Owner Authentication Routes */}
            <Route
              path="/business/auth/login"
              element={
                <PublicRoute restrictedFor="business_owner">
                  <BusinessLogin />
                </PublicRoute>
              }
            />
            <Route
              path="/business/auth/register"
              element={
                <PublicRoute restrictedFor="business_owner">
                  <BusinessRegister />
                </PublicRoute>
              }
            />
            <Route
              path="/business/auth/forgot-password"
              element={
                <PublicRoute restrictedFor="business_owner">
                  <BusinessForgotPassword />
                </PublicRoute>
              }
            />
            <Route
              path="/business/auth/reset-password"
              element={
                <PublicRoute restrictedFor="business_owner">
                  <BusinessResetPassword />
                </PublicRoute>
              }
            />

            {/* Super Admin Authentication Routes */}
            <Route
              path="/admin/auth/login"
              element={
                <PublicRoute restrictedFor="super_admin">
                  <AdminLogin />
                </PublicRoute>
              }
            />
            <Route
              path="/admin/auth/forgot-password"
              element={
                <PublicRoute restrictedFor="super_admin">
                  <AdminForgotPassword />
                </PublicRoute>
              }
            />
            <Route
              path="/admin/auth/reset-password"
              element={
                <PublicRoute restrictedFor="super_admin">
                  <AdminResetPassword />
                </PublicRoute>
              }
            />

            {/* Business Owner Protected Routes */}
            <Route
              path="/business/dashboard"
              element={
                <ProtectedRoute requiredRole="business_owner">
                  <BusinessDashboard />
                </ProtectedRoute>
              }
            />

            {/* Super Admin Protected Routes */}
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute requiredRole="super_admin">
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            {/* Legacy Business Routes (wrapped in Layout and protected) */}
            <Route
              path="/business/*"
              element={
                <ProtectedRoute requiredRole="business_owner">
                  <Layout>
                    <Routes>
                      <Route path="legacy-dashboard" element={<Dashboard />} />
                      <Route path="inbox" element={<Inbox />} />
                      <Route path="campaigns" element={<Campaigns />} />
                      <Route path="contacts" element={<Contacts />} />
                      <Route path="automation" element={<Automation />} />
                      <Route path="templates" element={<Templates />} />
                      <Route path="phone-numbers" element={<PhoneNumbers />} />
                      <Route path="analytics" element={<Analytics />} />
                      <Route path="billing" element={<Billing />} />
                      <Route path="settings" element={<Settings />} />
                    </Routes>
                  </Layout>
                </ProtectedRoute>
              }
            />

            {/* Catch all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
