import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserRole, LoginCredentials, RegisterData, AuthContextType } from '@/types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock demo credentials for testing
const DEMO_CREDENTIALS = {
  business_owner: {
    email: '<EMAIL>',
    password: 'demo123',
    user: {
      id: '1',
      email: '<EMAIL>',
      fullName: '<PERSON>',
      role: 'business_owner' as UserRole,
      businessName: 'Demo Business',
      phoneNumber: '+**********'
    }
  },
  super_admin: {
    email: '<EMAIL>',
    password: 'admin123',
    user: {
      id: '2',
      email: '<EMAIL>',
      fullName: 'Admin User',
      role: 'super_admin' as UserRole
    }
  }
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check for stored user on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('ayuchat_user');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('ayuchat_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (credentials: LoginCredentials, role: UserRole): Promise<boolean> => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // TODO: Send login credentials to backend and receive JWT/token
    // const response = await authAPI.login(credentials, role);
    
    // Mock validation
    const demoUser = DEMO_CREDENTIALS[role];
    if (credentials.email === demoUser.email && credentials.password === demoUser.password) {
      setUser(demoUser.user);
      localStorage.setItem('ayuchat_user', JSON.stringify(demoUser.user));
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const register = async (data: RegisterData): Promise<boolean> => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // TODO: Send registration data to backend API
    // const response = await authAPI.register(data);
    
    // Mock successful registration
    const newUser: User = {
      id: Date.now().toString(),
      email: data.email,
      fullName: data.fullName,
      role: 'business_owner',
      businessName: data.businessName,
      phoneNumber: data.phoneNumber
    };
    
    setUser(newUser);
    localStorage.setItem('ayuchat_user', JSON.stringify(newUser));
    setIsLoading(false);
    return true;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('ayuchat_user');
    // TODO: Invalidate token on backend
    // authAPI.logout();
  };

  const forgotPassword = async (email: string): Promise<boolean> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // TODO: Trigger backend to send password reset link to email
    // const response = await authAPI.forgotPassword(email);
    
    // Mock successful request
    return true;
  };

  const resetPassword = async (token: string, newPassword: string): Promise<boolean> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // TODO: Send new password and token to backend to update user password
    // const response = await authAPI.resetPassword(token, newPassword);
    
    // Mock successful reset
    return true;
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
