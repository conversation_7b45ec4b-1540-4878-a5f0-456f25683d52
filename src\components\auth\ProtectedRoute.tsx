import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredRole,
  redirectTo 
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center space-y-4">
          <div className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Loading...</h2>
            <p className="text-sm text-gray-600">Please wait while we verify your session</p>
          </div>
        </div>
      </div>
    );
  }

  // If not authenticated, redirect to appropriate login page
  if (!isAuthenticated || !user) {
    const loginPath = requiredRole === 'super_admin' 
      ? '/admin/auth/login' 
      : '/business/auth/login';
    
    return <Navigate to={redirectTo || loginPath} state={{ from: location }} replace />;
  }

  // If authenticated but wrong role, redirect to appropriate dashboard
  if (requiredRole && user.role !== requiredRole) {
    const dashboardPath = user.role === 'super_admin' 
      ? '/admin/dashboard' 
      : '/business/dashboard';
    
    return <Navigate to={dashboardPath} replace />;
  }

  // TODO: Replace with real token-based auth validation
  // In a real app, you would validate the JWT token here
  // const isValidToken = await validateToken(user.token);
  // if (!isValidToken) {
  //   logout();
  //   return <Navigate to="/login" replace />;
  // }

  return <>{children}</>;
};

export default ProtectedRoute;
