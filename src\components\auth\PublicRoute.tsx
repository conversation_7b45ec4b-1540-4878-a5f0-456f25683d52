import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { UserRole } from '@/types/auth';
import { Loader2 } from 'lucide-react';

interface PublicRouteProps {
  children: React.ReactNode;
  restrictedFor?: UserRole; // If specified, redirect authenticated users of this role
}

const PublicRoute: React.FC<PublicRouteProps> = ({ children, restrictedFor }) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center space-y-4">
          <div className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Loading...</h2>
            <p className="text-sm text-gray-600">Please wait while we check your session</p>
          </div>
        </div>
      </div>
    );
  }

  // If authenticated and accessing auth pages, redirect to appropriate dashboard
  if (isAuthenticated && user) {
    // If restrictedFor is specified and user matches that role, redirect
    if (restrictedFor && user.role === restrictedFor) {
      const dashboardPath = user.role === 'super_admin' 
        ? '/admin/dashboard' 
        : '/business/dashboard';
      return <Navigate to={dashboardPath} replace />;
    }
    
    // If no restriction specified, redirect any authenticated user
    if (!restrictedFor) {
      const dashboardPath = user.role === 'super_admin' 
        ? '/admin/dashboard' 
        : '/business/dashboard';
      return <Navigate to={dashboardPath} replace />;
    }
  }

  return <>{children}</>;
};

export default PublicRoute;
