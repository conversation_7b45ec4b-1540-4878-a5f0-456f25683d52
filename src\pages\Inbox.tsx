import React, { useState } from "react";
import {
  Search,
  Filter,
  MoreVertical,
  Phone,
  Video,
  Send,
  Paperclip,
  Smile,
} from "lucide-react";
import { Input } from "@/components/shared/ui/input";
import { But<PERSON> } from "@/components/shared/ui/button";

// Types
interface Message {
  id: string;
  content: string;
  timestamp: string;
  sender: "user" | "agent";
  type: "text" | "image" | "document";
  status: "sent" | "delivered" | "read";
}

interface Conversation {
  id: string;
  name: string;
  lastMessage: string;
  time: string;
  unread: number;
  status: "online" | "offline";
  avatar: string;
  phone?: string;
}

// ConversationList Component
const ConversationList: React.FC<{
  conversations: Conversation[];
  activeConversation: string | null;
  onSelectConversation: (id: string) => void;
}> = ({ conversations, activeConversation, onSelectConversation }) => {
  return (
    <div className="flex-1 overflow-y-auto">
      {conversations.map((conversation) => (
        <div
          key={conversation.id}
          onClick={() => onSelectConversation(conversation.id)}
          className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
            activeConversation === conversation.id
              ? "bg-teal-50 border-teal-200"
              : ""
          }`}
        >
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-medium">
                {conversation.avatar}
              </div>
              {conversation.status === "online" && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {conversation.name}
                </p>
                <p className="text-xs text-gray-500">{conversation.time}</p>
              </div>
              <p className="text-sm text-gray-500 truncate mt-1">
                {conversation.lastMessage}
              </p>
            </div>
            {conversation.unread > 0 && (
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-medium">
                  {conversation.unread}
                </span>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

// IncomingMessage Component
const IncomingMessage: React.FC<{ message: Message; avatar: string }> = ({
  message,
  avatar,
}) => {
  return (
    <div className="flex items-start space-x-2">
      <div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white text-sm">
        {avatar}
      </div>
      <div className="bg-white rounded-lg p-3 shadow-sm max-w-xs">
        <p className="text-sm text-gray-900">{message.content}</p>
        <p className="text-xs text-gray-500 mt-1">{message.timestamp}</p>
      </div>
    </div>
  );
};

// OutgoingMessage Component
const OutgoingMessage: React.FC<{ message: Message }> = ({ message }) => {
  return (
    <div className="flex items-start space-x-2 flex-row-reverse">
      <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm">
        You
      </div>
      <div className="bg-teal-600 text-white rounded-lg p-3 shadow-sm max-w-xs">
        <p className="text-sm">{message.content}</p>
        <div className="flex items-center justify-between mt-1">
          <p className="text-xs text-teal-100">{message.timestamp}</p>
          <div className="flex space-x-1">
            {message.status === "sent" && (
              <div className="w-1 h-1 bg-teal-200 rounded-full"></div>
            )}
            {message.status === "delivered" && (
              <>
                <div className="w-1 h-1 bg-teal-200 rounded-full"></div>
                <div className="w-1 h-1 bg-teal-200 rounded-full"></div>
              </>
            )}
            {message.status === "read" && (
              <>
                <div className="w-1 h-1 bg-blue-300 rounded-full"></div>
                <div className="w-1 h-1 bg-blue-300 rounded-full"></div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// MessageInput Component
const MessageInput: React.FC<{
  onSendMessage: (message: string) => void;
}> = ({ onSendMessage }) => {
  const [message, setMessage] = useState("");

  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message);
      setMessage("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="p-4 bg-white border-t border-gray-200">
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-700"
        >
          <Paperclip className="w-4 h-4" />
        </Button>
        <Input
          type="text"
          placeholder="Type your message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          className="flex-1"
        />
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-500 hover:text-gray-700"
        >
          <Smile className="w-4 h-4" />
        </Button>
        <Button
          onClick={handleSend}
          className="bg-teal-600 hover:bg-teal-700 text-white"
          disabled={!message.trim()}
        >
          <Send className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

// ChatArea Component
const ChatArea: React.FC<{
  activeConversation: Conversation | null;
  messages: Message[];
  onSendMessage: (message: string) => void;
}> = ({ activeConversation, messages, onSendMessage }) => {
  if (!activeConversation) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <MoreVertical className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Select a conversation
          </h3>
          <p className="text-gray-500">
            Choose a conversation from the list to start messaging
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Chat Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-medium">
                {activeConversation.avatar}
              </div>
              {activeConversation.status === "online" && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">
                {activeConversation.name}
              </h3>
              <p className="text-sm text-gray-500">
                {activeConversation.status === "online" ? "Online" : "Offline"}{" "}
                • Last seen {activeConversation.time}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <Phone className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <Video className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <MoreVertical className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 p-4 bg-gray-50 overflow-y-auto">
        <div className="space-y-4">
          {messages.map((message) => (
            <div key={message.id}>
              {message.sender === "user" ? (
                <IncomingMessage
                  message={message}
                  avatar={activeConversation.avatar}
                />
              ) : (
                <OutgoingMessage message={message} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Message Input */}
      <MessageInput onSendMessage={onSendMessage} />
    </div>
  );
};

// Main Inbox Component
const Inbox: React.FC = () => {
  const [activeConversation, setActiveConversation] = useState<string | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState("");

  // Mock data
  const conversations: Conversation[] = [
    {
      id: "1",
      name: "John Doe",
      lastMessage: "Thanks for the quick response!",
      time: "2 min ago",
      unread: 2,
      status: "online",
      avatar: "JD",
      phone: "+1234567890",
    },
    {
      id: "2",
      name: "Sarah Wilson",
      lastMessage: "Can you send me the product catalog?",
      time: "1 hour ago",
      unread: 0,
      status: "offline",
      avatar: "SW",
      phone: "+1234567891",
    },
    {
      id: "3",
      name: "Mike Johnson",
      lastMessage: "I'd like to place an order",
      time: "3 hours ago",
      unread: 1,
      status: "online",
      avatar: "MJ",
      phone: "+1234567892",
    },
  ];

  const mockMessages: { [key: string]: Message[] } = {
    "1": [
      {
        id: "m1",
        content:
          "Hi! I'm interested in your product catalog. Can you share it with me?",
        timestamp: "10:30 AM",
        sender: "user",
        type: "text",
        status: "read",
      },
      {
        id: "m2",
        content:
          "Sure! I'll send you our latest catalog right away. It includes all our new products for this season.",
        timestamp: "10:32 AM",
        sender: "agent",
        type: "text",
        status: "read",
      },
      {
        id: "m3",
        content: "Thanks for the quick response!",
        timestamp: "10:35 AM",
        sender: "user",
        type: "text",
        status: "read",
      },
    ],
    "2": [
      {
        id: "m4",
        content: "Can you send me the product catalog?",
        timestamp: "9:30 AM",
        sender: "user",
        type: "text",
        status: "delivered",
      },
    ],
    "3": [
      {
        id: "m5",
        content: "I'd like to place an order",
        timestamp: "7:30 AM",
        sender: "user",
        type: "text",
        status: "sent",
      },
    ],
  };

  const filteredConversations = conversations.filter(
    (conv) =>
      conv.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conv.lastMessage.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedConversation = conversations.find(
    (conv) => conv.id === activeConversation
  );
  const currentMessages = activeConversation
    ? mockMessages[activeConversation] || []
    : [];

  const handleSendMessage = (messageContent: string) => {
    if (!activeConversation) return;

    const newMessage: Message = {
      id: `m${Date.now()}`,
      content: messageContent,
      timestamp: new Date().toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      }),
      sender: "agent",
      type: "text",
      status: "sent",
    };

    // In a real app, this would update the state/store
    console.log("Sending message:", newMessage);
  };

  return (
    <div className="h-[calc(100vh-8rem)] flex bg-white rounded-xl border border-gray-200 overflow-hidden">
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Conversations
            </h2>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-500 hover:text-gray-700"
            >
              <Filter className="w-4 h-4" />
            </Button>
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search conversations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-gray-50 border-0"
            />
          </div>
        </div>

        {/* Conversation List */}
        <ConversationList
          conversations={filteredConversations}
          activeConversation={activeConversation}
          onSelectConversation={setActiveConversation}
        />
      </div>

      {/* Chat Area */}
      <ChatArea
        activeConversation={selectedConversation || null}
        messages={currentMessages}
        onSendMessage={handleSendMessage}
      />
    </div>
  );
};

export default Inbox;
