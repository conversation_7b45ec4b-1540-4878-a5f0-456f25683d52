# 🚀 AyuChat WhatsApp Marketing Platform - Complete Frontend Development Prompt

## 📋 Project Overview
Build a complete frontend-only SaaS application called **AyuChat** - a WhatsApp Marketing Automation Platform. This is a dual-role system supporting Business Owners (clients) and Super Admins (internal staff). Use React + Vite with React Router v6+ and Tailwind CSS for styling.

## 🎯 Core Requirements
- **Frontend-only development** - Add `// TODO: Backend integration needed` comments where APIs will connect
- **Dual-role authentication system** with separate routing
- **Mobile-responsive design** using Tailwind CSS
- **Clean, modern UI** with proper loading states and error handling

---

## 🔐 PHASE 1: Authentication System

### 1. Registration Page (Business Owners Only)
**Route:** `/business/auth/register`

**Form Fields:**
- Full Name (required)
- Email (required) 
- Business Name (required)
- Phone Number (optional)
- Password (min. 6 characters, with confirmation)

**Functionality:**
- Green "Create Account" button with loading state
- Form validation with error messages
- Success redirect to `/business/dashboard`
- Add comment: `// TODO: Send registration data to backend API`

### 2. Login Pages (Separate for Each Role)
**Routes:** 
- `/business/auth/login` (Business Owner)
- `/admin/auth/login` (Super Admin)

**Features:**
- Email and Password fields
- Green "Sign In" button with loading state
- "Forgot Password?" link
- Demo credentials display for testing
- Role-based redirect after login
- Add comment: `// TODO: Authenticate with backend and receive JWT token`

### 3. Password Reset Flow
**Routes:**
- `/business/auth/forgot-password` & `/admin/auth/forgot-password`
- `/business/auth/reset-password?token=...` & `/admin/auth/reset-password?token=...`

**Features:**
- Email input for forgot password
- New password + confirm password for reset
- Success messages and redirects
- Add comment: `// TODO: Integrate with backend password reset API`

### 4. Route Protection & Context
- Create AuthContext for user state management
- Protect routes based on user roles
- Business routes: `/business/*`
- Admin routes: `/admin/*`
- Add comment: `// TODO: Implement JWT token validation`

---

## 📱 PHASE 2: Core Platform Features

### 🗂️ Templates Module
**Route:** `/business/templates`

**Features Required:**
1. **Search & Filter System:**
   - Search bar for template name/keyword search
   - Filter dropdowns for Status (Pending, Approved, Rejected)
   - Filter by Category (Offers, OTPs, Alerts, etc.)

2. **Template Management:**
   - Editable templates table with Action buttons
   - "View Template" modal showing full template details
   - Create new template form with:
     - Template name (lowercase, no spaces)
     - Category selection (Marketing, Utility, Authentication)
     - Language selection
     - Header (Text/Image/Video/Document)
     - Body message with variables {{1}}, {{2}}
     - Footer text
     - Buttons (Quick Replies or Call-to-Action)
   - Preview functionality with dummy data
   - Template status tracking

3. **Pre-built Template Examples:**
   - Order confirmation template
   - Appointment reminder template  
   - Payment reminder template
   - Festival offer campaign template
   - Feedback request template

**Comments to Add:**
```javascript
// TODO: Connect to WhatsApp Cloud API for template submission
// TODO: Fetch template status from backend
// TODO: Save template data to database
```

### ☎️ Phone Numbers Section
**Route:** `/business/phone-numbers`

**Features Required:**
1. **Phone Number Management:**
   - Functional search bar for numbers/names
   - "View Details" button opening modal with WABA details
   - "Configure" button with step-by-step Meta WhatsApp setup
   - "Add Phone Number" modal for new number registration

**Comments to Add:**
```javascript
// TODO: Integrate with Meta WhatsApp Business API
// TODO: Fetch phone number configurations from backend
// TODO: Save phone number settings to database
```

### 📊 Analytics Section  
**Route:** `/business/analytics`

**Features Required:**
1. **Date Range Functionality:**
   - Working date picker with "From" and "To" date selection
   - Analytics data filtering based on selected dates

2. **Export Functionality:**
   - "Export Report" button generating Excel files
   - Data export filtered by selected date range

**Comments to Add:**
```javascript
// TODO: Fetch analytics data from backend API
// TODO: Generate Excel reports server-side
```

### 💳 Billing Section
**Route:** `/business/billing`

**Features Required:**
1. **Plan Management:**
   - "Change Plan" button with plan selection modal
   - Payment processing flow for plan upgrades
   - "Cancel Subscription" functionality
   - Current plan display (Starter, Pro, Enterprise)

2. **Invoice Management:**
   - Download invoices in Excel format
   - Display amounts in Indian Rupees (₹)
   - Usage tracking (messages sent, contact limits)

3. **Restrictions:**
   - **DO NOT include "Update Payment Method" anywhere**
   - Remove any payment method update sections

**Comments to Add:**
```javascript
// TODO: Integrate with payment gateway for plan changes
// TODO: Connect to billing API for invoice generation
// TODO: Fetch subscription data from backend
```

### ⚙️ Settings Section
**Route:** `/business/settings`

**Features Required:**
1. **Profile Tab:**
   - **Email and Phone fields must be READ-ONLY**
   - All other profile fields editable
   - Save changes functionality

2. **Billing Tab:**
   - **Remove payment method section completely**
   - Show current subscription details only

**Comments to Add:**
```javascript
// TODO: Update profile data via backend API
// TODO: Fetch user profile from database
```

---

## 🎨 UI/UX Requirements

### Design Standards:
- **Tailwind CSS** for all styling
- **Mobile-responsive** design
- **Loading states** for all buttons and forms
- **Error handling** with user-friendly messages
- **Success notifications** for completed actions
- **Clean card-based layouts** for forms
- **Consistent color scheme** with green primary buttons

### Component Structure:
- Reusable form components
- Modal components for details/actions
- Table components with search/filter
- Dashboard layout with sidebar navigation
- Responsive navigation for mobile

---

## 🔧 Technical Implementation Notes

### Required Dependencies:
```json
{
  "react": "^18.0.0",
  "react-router-dom": "^6.0.0",
  "tailwindcss": "^3.0.0",
  "react-hook-form": "^7.0.0",
  "date-fns": "^2.0.0"
}
```

### Folder Structure:
```
src/
├── components/
│   ├── auth/
│   ├── templates/
│   ├── analytics/
│   ├── billing/
│   └── settings/
├── pages/
│   ├── business/
│   └── admin/
├── context/
├── hooks/
└── utils/
```

### Mock Data Requirements:
- Sample templates for each category
- Demo user accounts for testing
- Sample analytics data
- Mock billing/subscription data

---

## ✅ Acceptance Criteria

1. **Authentication flows work completely** with proper redirects
2. **All forms have validation** and error handling
3. **Search and filter functionality** works on all modules
4. **Modals and buttons are functional** as specified
5. **No backend connections** - only frontend with TODO comments
6. **Mobile responsive** on all screen sizes
7. **Clean, professional UI** matching modern SaaS standards

---

**Final Note:** Build exactly what's specified above. Do not add extra features or modify requirements. Focus on creating a polished, functional frontend that's ready for backend integration.
